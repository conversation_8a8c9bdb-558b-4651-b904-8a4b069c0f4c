from typing import List

from sqlalchemy.exc import IntegrityError
from src.utils.exceptions import (
    DuplicateError,
    ForeignKeyNotFoundError,
    ForeignKeyViolationError,
    NotFoundError,
)

from ..contracts import IReportReviewHistoryRepository
from ..entities import (
    CreateReportReviewHistoryEntity,
    ReportReviewHistoryEntity,
    UpdateReportReviewHistoryEntity,
)


class ReportReviewHistoryService:
    def __init__(
        self,
        report_review_history_repository: IReportReviewHistoryRepository,
        monthly_report_service=None,
        monthly_report_supervisor_service=None,
        supervisor_contract_service=None,
        report_review_status_service=None,
    ):
        self._repository = report_review_history_repository
        self._monthly_report_service = monthly_report_service
        self._monthly_report_supervisor_service = monthly_report_supervisor_service
        self._supervisor_contract_service = supervisor_contract_service
        self._report_review_status_service = report_review_status_service

    def get_all(self) -> List[ReportReviewHistoryEntity]:
        return self._repository.get_all()

    def get_by_id(self, id: int) -> ReportReviewHistoryEntity:
        entity = self._repository.get_by_id(id)
        if entity is None:
            raise NotFoundError(f"ReportReviewHistory with id {id} not found")
        return entity

    def get_by_monthly_report_id(
        self, monthly_report_id: int
    ) -> List[ReportReviewHistoryEntity]:
        return self._repository.get_by_monthly_report_id(monthly_report_id)

    def create(
        self, create_entity: CreateReportReviewHistoryEntity
    ) -> ReportReviewHistoryEntity:
        try:
            created_entity = self._repository.create(create_entity)

            try:
                if (
                    self._monthly_report_service
                    and self._monthly_report_supervisor_service
                    and self._supervisor_contract_service
                    and self._report_review_status_service
                ):

                    monthly_report = self._monthly_report_service.get_by_id(
                        create_entity.monthly_report_id
                    )

                    status = self._report_review_status_service.get_by_id(
                        create_entity.review_status_id
                    )

                    if status.name.lower() == "pendiente de revisión":
                        supervisor_contracts = (
                            self._supervisor_contract_service.get_by_contract_id(
                                monthly_report.contractor_contract.contract_id
                            )
                        )

                        has_double_supervision = len(supervisor_contracts) >= 2

                        from src.features.monthly_report.domain.entities.update_monthly_report_entity import (
                            UpdateMonthlyReportEntity,
                        )

                        update_entity = UpdateMonthlyReportEntity(
                            double_supervision=has_double_supervision
                        )
                        self._monthly_report_service.update(
                            create_entity.monthly_report_id, update_entity
                        )

                    if status.name.lower() == "aprobado":
                        if (
                            monthly_report.contractor_contract
                            and monthly_report.contractor_contract.contract
                        ):
                            contract = monthly_report.contractor_contract.contract
                            supervisor_contracts = (
                                self._supervisor_contract_service.get_by_contract_id(
                                    contract.id
                                )
                            )

                            if supervisor_contracts:
                                current_supervisors = self._monthly_report_supervisor_service.get_by_monthly_report_id(
                                    create_entity.monthly_report_id
                                )

                                for i, sc in enumerate(supervisor_contracts[:2]):
                                    if sc.supervisor:
                                        matching_supervisor = next(
                                            (
                                                s
                                                for s in current_supervisors
                                                if s.supervisor_id_number
                                                == str(sc.supervisor.id_number)
                                            ),
                                            None,
                                        )

                                        from src.features.monthly_report_supervisor.domain.entities.monthly_report_supervisor_entity import (
                                            CreateMonthlyReportSupervisorEntity,
                                            UpdateMonthlyReportSupervisorEntity,
                                        )

                                        if matching_supervisor:
                                            update_entity = UpdateMonthlyReportSupervisorEntity(
                                                approval_date=create_entity.review_date
                                            )
                                            self._monthly_report_supervisor_service.update(
                                                matching_supervisor.id, update_entity
                                            )
                                        else:
                                            new_entity = CreateMonthlyReportSupervisorEntity(
                                                monthly_report_id=create_entity.monthly_report_id,
                                                supervisor_id_number=str(
                                                    sc.supervisor.id_number
                                                ),
                                                supervisor_full_name=sc.supervisor.full_name,
                                                supervisor_position=sc.supervisor.position,
                                                approval_date=create_entity.review_date,
                                            )
                                            self._monthly_report_supervisor_service.create(
                                                new_entity
                                            )

                    elif status.name.lower() == "rechazado":
                        current_supervisors = self._monthly_report_supervisor_service.get_by_monthly_report_id(
                            create_entity.monthly_report_id
                        )
                        for supervisor in current_supervisors:
                            self._monthly_report_supervisor_service.delete(
                                supervisor.id
                            )

            except Exception:
                pass

            return created_entity
        except IntegrityError as e:
            if "duplicate key value violates unique constraint" in str(e):
                raise DuplicateError(
                    "ReportReviewHistory with these details already exists"
                )
            elif "violates foreign key constraint" in str(e):
                raise ForeignKeyNotFoundError("Related entity not found.")
            else:
                raise ForeignKeyViolationError(
                    f"Error creating ReportReviewHistory: {str(e)}"
                )

    def update(
        self, id: int, update_entity: UpdateReportReviewHistoryEntity
    ) -> ReportReviewHistoryEntity:
        self.get_by_id(id)
        try:
            return self._repository.update(id, update_entity)
        except IntegrityError as e:
            if "duplicate key value violates unique constraint" in str(e):
                raise DuplicateError(
                    "ReportReviewHistory with these details already exists"
                )
            elif "violates foreign key constraint" in str(e):
                raise ForeignKeyNotFoundError("Related entity not found.")
            else:
                raise ForeignKeyViolationError(
                    f"Error updating ReportReviewHistory: {str(e)}"
                )

    def delete(self, id: int) -> None:
        self.get_by_id(id)
        try:
            self._repository.delete(id)
        except IntegrityError:
            raise ForeignKeyViolationError(
                f"ReportReviewHistory with id {id} is still referenced by other tables and cannot be deleted."
            )
