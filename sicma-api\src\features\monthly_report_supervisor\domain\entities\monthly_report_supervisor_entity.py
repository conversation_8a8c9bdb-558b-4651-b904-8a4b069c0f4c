from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class MonthlyReportSupervisorEntity(BaseModel):
    id: Optional[int] = None
    monthly_report_id: int
    supervisor_id_number: str = Field(..., min_length=1, max_length=50)
    supervisor_full_name: str = Field(..., min_length=1, max_length=255)
    supervisor_position: Optional[str] = Field(None, max_length=255)
    approval_date: Optional[datetime] = None

    model_config = {"from_attributes": True}


class CreateMonthlyReportSupervisorEntity(BaseModel):
    monthly_report_id: int
    supervisor_id_number: str = Field(..., min_length=1, max_length=50)
    supervisor_full_name: str = Field(..., min_length=1, max_length=255)
    supervisor_position: Optional[str] = Field(None, max_length=255)
    approval_date: Optional[datetime] = None


class UpdateMonthlyReportSupervisorEntity(BaseModel):
    monthly_report_id: Optional[int] = None
    supervisor_id_number: Optional[str] = Field(None, min_length=1, max_length=50)
    supervisor_full_name: Optional[str] = Field(None, min_length=1, max_length=255)
    supervisor_position: Optional[str] = Field(None, max_length=255)
    approval_date: Optional[datetime] = None
