from datetime import datetime
from typing import Optional

from humps import camelize
from pydantic import BaseModel


class MonthlyReportSupervisorDto(BaseModel):
    id: Optional[int] = None
    monthly_report_id: int
    supervisor_id_number: str
    supervisor_full_name: str
    supervisor_position: Optional[str] = None
    approval_date: Optional[datetime] = None

    model_config = {
        "from_attributes": True,
        "alias_generator": camelize,
        "populate_by_name": True,
    }


class CreateMonthlyReportSupervisorDto(BaseModel):
    monthly_report_id: int
    supervisor_id_number: str
    supervisor_full_name: str
    supervisor_position: Optional[str] = None
    approval_date: Optional[datetime] = None

    model_config = {
        "from_attributes": True,
        "alias_generator": camelize,
        "populate_by_name": True,
    }


class UpdateMonthlyReportSupervisorDto(BaseModel):
    monthly_report_id: Optional[int] = None
    supervisor_id_number: Optional[str] = None
    supervisor_full_name: Optional[str] = None
    supervisor_position: Optional[str] = None
    approval_date: Optional[datetime] = None

    model_config = {
        "from_attributes": True,
        "alias_generator": camelize,
        "populate_by_name": True,
    }
