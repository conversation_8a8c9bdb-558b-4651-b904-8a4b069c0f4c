from sqlalchemy import Column, DateTime, <PERSON><PERSON>ey, Integer, String
from sqlalchemy.orm import relationship
from src.utils.databases.sqlalchemy_orm import Base


class MonthlyReportSupervisorModel(Base):
    __tablename__ = "monthly_report_supervisor"

    id = Column(Integer, primary_key=True)
    monthly_report_id = Column(Integer, ForeignKey("monthly_report.id"), nullable=False)
    supervisor_id_number = Column(String(50), nullable=False)
    supervisor_full_name = Column(String(255), nullable=False)
    supervisor_position = Column(String(255), nullable=True)
    approval_date = Column(DateTime, nullable=True)

    monthly_report = relationship(
        "MonthlyReportModel", back_populates="monthly_report_supervisors"
    )
