import os
import subprocess
from io import BytesIO
from pathlib import Path

import requests
from openpyxl import load_workbook
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
from PIL import Image as PILImage
from PIL import ImageDraw, ImageFont
from src.features.initial_report_documentation.domain.services import (
    InitialReportDocumentationService,
)
from src.features.monthly_report.domain.contracts import IMonthlyReportRepository
from src.features.monthly_report_ccp.domain.services.monthly_report_ccp_service import (
    MonthlyReportCCPService,
)
from src.features.monthly_report_supervisor.domain.services.monthly_report_supervisor_service import (
    MonthlyReportSupervisorService,
)
from src.features.obligation.domain.services.obligation_service import ObligationService
from src.features.payment.domain.services import PaymentService
from src.features.period.domain.services.period_service import PeriodService
from src.features.report_obligation.domain.services import ReportObligationService
from src.features.social_security_contribution.domain.services import (
    SocialSecurityContributionService,
)
from src.features.supervisor_contract.domain.services.supervisor_contract_service import (
    SupervisorContractService,
)
from src.utils import format_date
from src.utils.exceptions import NotFoundError, ValidationError
from src.utils.services.local_file_service import LocalFileService

from ..entities import MonthlyReportEntity


class MonthlyReportPDFGeneratorService:
    def __init__(
        self,
        obligation_service: ObligationService,
        report_obligation_service: ReportObligationService,
        supervisor_contract_service: SupervisorContractService,
        payment_service: PaymentService,
        monthly_report_repository: IMonthlyReportRepository,
        social_security_contribution_service: SocialSecurityContributionService,
        initial_report_documentation_service: InitialReportDocumentationService,
        file_storage_service: LocalFileService,
        period_service: PeriodService,
        monthly_report_ccp_service: MonthlyReportCCPService,
        monthly_report_supervisor_service: MonthlyReportSupervisorService,
    ):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.template_dir = os.path.join(current_dir, "..", "templates")
        self.assets_dir = os.path.join(current_dir, "..", "assets")
        self._obligation_service = obligation_service
        self._report_obligation_service = report_obligation_service
        self._supervisor_contract_service = supervisor_contract_service
        self._payment_service = payment_service
        self._monthly_report_repository = monthly_report_repository
        self._social_security_contribution_service = (
            social_security_contribution_service
        )
        self._initial_report_documentation_service = (
            initial_report_documentation_service
        )
        self._file_storage_service = file_storage_service
        self._period_service = period_service
        self._monthly_report_ccp_service = monthly_report_ccp_service
        self._monthly_report_supervisor_service = monthly_report_supervisor_service

    def write_to_cell(self, ws, cell_ref, value):
        cell = ws[cell_ref]
        if cell.coordinate in ws.merged_cells:
            for merged_range in ws.merged_cells.ranges:
                if cell.coordinate in merged_range:
                    top_left = f"{get_column_letter(merged_range.min_col)}{merged_range.min_row}"
                    if value:
                        ws[top_left] = value
                    break
        else:
            if value:
                cell.value = value

    def _download_font_if_needed(self):
        font_path = os.path.join(
            os.path.dirname(__file__), "..", "templates", "LibreBaskerville-Italic.ttf"
        )
        if not os.path.exists(font_path):
            font_url = "https://github.com/google/fonts/raw/main/ofl/librebaskerville/LibreBaskerville-Italic.ttf"
            response = requests.get(font_url)
            if response.status_code == 200:
                os.makedirs(os.path.dirname(font_path), exist_ok=True)
                with open(font_path, "wb") as f:
                    f.write(response.content)
        return font_path

    def _create_signature_with_text(
        self, name: str, status_text: str = None, status_date: str = None
    ) -> BytesIO:
        logo_path = os.path.join(
            os.path.dirname(__file__), "..", "templates", "logo.png"
        )
        width = 500
        height = 80
        background = PILImage.open(logo_path).convert("RGBA")
        background = background.resize((width, height))
        draw = ImageDraw.Draw(background)
        try:
            font_path = self._download_font_if_needed()
            font = ImageFont.truetype(font_path, 24)
            small_font = ImageFont.truetype(font_path, 16)
        except:
            font = ImageFont.load_default()
            small_font = ImageFont.load_default()
        name_parts = name.split()
        formatted_name = " ".join(word.capitalize() for word in name_parts)

        def split_text_into_lines(text, max_width):
            words = text.split()
            lines = []
            current_line = []
            for word in words:
                test_line = " ".join(current_line + [word])
                bbox = draw.textbbox((0, 0), test_line, font=font)
                width = bbox[2] - bbox[0]
                if width <= max_width:
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(" ".join(current_line))
                        current_line = [word]
                    else:
                        lines.append(word)
                        current_line = []
            if current_line:
                lines.append(" ".join(current_line))
            return lines

        max_width = 300
        lines = split_text_into_lines(formatted_name, max_width)
        line_spacing = 4
        total_height = sum(
            draw.textbbox((0, 0), line, font=font)[3]
            - draw.textbbox((0, 0), line, font=font)[1]
            for line in lines
        )
        total_height += line_spacing * (len(lines) - 1)
        y = (background.height - total_height) // 2 - 5
        for line in lines:
            bbox = draw.textbbox((0, 0), line, font=font)
            text_width = bbox[2] - bbox[0]
            x = 20 + (max_width - text_width) // 2
            draw.text((x, y), line, font=font, fill=(0, 0, 0, 255))
            y += bbox[3] - bbox[1] + line_spacing
        if status_text and status_date:
            status_full = f"{status_text}\n{status_date}"
            status_lines = status_full.split("\n")
            sy = (
                background.height
                - (len(status_lines) * 18 + (len(status_lines) - 1) * 2)
            ) // 2
            for sline in status_lines:
                sbbox = draw.textbbox((0, 0), sline, font=small_font)
                sx = 340
                draw.text((sx, sy), sline, font=small_font, fill=(0, 0, 0, 180))
                sy += sbbox[3] - sbbox[1] + 2
        output = BytesIO()
        background.save(output, format="PNG")
        output.seek(0)
        return output

    def _insert_contractor_signature(self, ws, signature_key: str, monthly_report=None):
        contractor_name = ws["C8"].value
        status_date = None

        if monthly_report and monthly_report.review_history:
            for rh in monthly_report.review_history:
                if rh.review_status.name.lower() == "pendiente de revisión":
                    status_date = rh.review_date.strftime("%d/%m/%Y %H:%M")
                    break

        img_buffer = self._create_signature_with_text(
            contractor_name,
            status_text="Verificado" if status_date else None,
            status_date=status_date,
        )

        img = Image(img_buffer)
        img.width = 500
        img.height = 80

        ws.add_image(img, "B85")

        self.write_to_cell(ws, "B88", f"NOMBRE: {contractor_name}")
        self.write_to_cell(ws, "B89", f"C.C.: {ws['C9'].value}")

    def _insert_supervisor_signature(
        self,
        ws,
        signature_key: str,
        cell_ref: str,
        supervisor_name: str = None,
        monthly_report=None,
        approval_date: str = None,
    ):
        name_to_show = supervisor_name if supervisor_name else "Supervisor"
        status_date = approval_date

        if not status_date and monthly_report and monthly_report.review_history:
            for rh in monthly_report.review_history:
                if rh.review_status.name.lower() == "aprobado":
                    status_date = rh.review_date.strftime("%d/%m/%Y %H:%M")
                    break

        img_buffer = self._create_signature_with_text(
            name_to_show,
            status_text="Aprobado" if status_date else None,
            status_date=status_date,
        )

        img = Image(img_buffer)
        img.width = 500
        img.height = 80

        ws.add_image(img, cell_ref)

    def generate_monthly_report_pdf(
        self,
        monthly_report: MonthlyReportEntity,
    ) -> bytes:
        social_security = None
        initial_report = None
        try:
            social_security = (
                self._social_security_contribution_service.get_by_monthly_report_id(
                    monthly_report.id
                )
            )
        except NotFoundError:
            pass
        try:
            initial_report = self._initial_report_documentation_service.get_by_contractor_contract_id(
                monthly_report.contractor_contract_id
            )
        except NotFoundError:
            pass
        obligations = self._report_obligation_service.get_by_monthly_report_id(
            monthly_report.id
        )
        detailed_obligations = []
        for ro in obligations:
            obligation_detail = self._obligation_service.get_by_id(ro.obligation_id)
            detailed_obligations.append(
                {"report_obligation": ro, "obligation_detail": obligation_detail}
            )

        detailed_obligations.sort(
            key=lambda x: (
                x["obligation_detail"].number is None,
                x["obligation_detail"].number,
            )
        )

        excel_path = Path(self.template_dir) / "monthly_report_template.xlsx"
        filled_excel_path = Path(self.template_dir) / "temp_report.xlsx"
        if not excel_path.exists():
            raise ValidationError(f"Excel template not found at: {excel_path}")
        wb = load_workbook(excel_path, data_only=False)
        ws = wb.active
        execution_months = (
            str(monthly_report.contract_duration_days // 30)
            if monthly_report.contract_duration_days
            else "N/A"
        )
        try:
            contract = monthly_report.contractor_contract.contract

            initial_value = 0
            additions_value = 0
            cdp_number = "N/A"
            rp_number = "N/A"

            if contract and contract.contract_values:
                sorted_contract_values = sorted(
                    contract.contract_values, key=lambda cv: cv.id
                )
                if sorted_contract_values:
                    initial_value = sorted_contract_values[0].numeric_value
                    cdp_number = sorted_contract_values[0].cdp
                    rp_number = sorted_contract_values[0].rp
                if len(sorted_contract_values) > 1:
                    additions_value = sum(
                        cv.numeric_value for cv in sorted_contract_values[1:]
                    )

            try:
                formatted_creation_date = format_date(monthly_report.creation_date)
                self.write_to_cell(ws, "E5", monthly_report.contract_dependency)
                self.write_to_cell(ws, "M5", formatted_creation_date)
                self.write_to_cell(ws, "C8", monthly_report.contractor_full_name)
                self.write_to_cell(ws, "C9", monthly_report.contractor_id_number)
                self.write_to_cell(ws, "G9", monthly_report.contractor_email)
                self.write_to_cell(ws, "M9", monthly_report.contractor_phone)
                self.write_to_cell(ws, "C11", monthly_report.contract_number)
                self.write_to_cell(ws, "E11", monthly_report.contract_year)
                self.write_to_cell(ws, "G11", cdp_number)
                self.write_to_cell(ws, "I11", rp_number)
                self.write_to_cell(
                    ws, "C12", monthly_report.contractor_contract.contract.object
                )
                self.write_to_cell(ws, "C13", monthly_report.report_number)
                formatted_start_date = format_date(monthly_report.start_date)
                formatted_end_date = format_date(monthly_report.end_date)
                self.write_to_cell(
                    ws,
                    "G13",
                    f"{formatted_start_date} a {formatted_end_date}",
                )
                self.write_to_cell(
                    ws,
                    "K13",
                    f"{execution_months} MESES" if execution_months != "N/A" else "N/A",
                )
                formatted_contract_start = format_date(
                    monthly_report.contract_start_date
                )
                formatted_contract_end = format_date(monthly_report.contract_end_date)
                self.write_to_cell(ws, "L11", formatted_contract_start)
                self.write_to_cell(ws, "M13", formatted_contract_end)
            except AttributeError as e:
                raise ValueError(f"Missing required data field: {str(e)}")
            if initial_report:
                self.write_to_cell(
                    ws,
                    "B30",
                    (
                        initial_report.prepaid_medicine_annual_payment
                        if initial_report.has_prepaid_medicine
                        else "N/A"
                    ),
                )
                self.write_to_cell(
                    ws,
                    "C30",
                    (
                        "SI__X__   NO____"
                        if initial_report.has_dependents
                        else "SI____   NO__X__"
                    ),
                )
                self.write_to_cell(
                    ws,
                    "D30",
                    (
                        initial_report.housing_interest_annual_payment
                        if initial_report.has_housing_interest
                        else "N/A"
                    ),
                )
                self.write_to_cell(
                    ws,
                    "E30",
                    (
                        initial_report.afc_account_annual_payment
                        if initial_report.has_afc_account
                        else "N/A"
                    ),
                )
                self.write_to_cell(
                    ws,
                    "F30",
                    (
                        initial_report.voluntary_savings_annual_payment
                        if initial_report.has_voluntary_savings
                        else "N/A"
                    ),
                )
            self.write_to_cell(
                ws,
                "B34",
                social_security.payment_form_number if social_security else "N/A",
            )
            self.write_to_cell(
                ws, "C34", social_security.ibc if social_security else "N/A"
            )
            self.write_to_cell(
                ws,
                "D34",
                social_security.health_contribution if social_security else "N/A",
            )
            self.write_to_cell(
                ws,
                "E34",
                social_security.pension_contribution if social_security else "N/A",
            )
            self.write_to_cell(
                ws,
                "F34",
                social_security.arl_contribution if social_security else "N/A",
            )
            self.write_to_cell(
                ws,
                "G34",
                (
                    social_security.compensation_fund_contribution
                    if social_security
                    and social_security.compensation_fund_contribution is not None
                    else "N/A"
                ),
            )
            self.write_to_cell(
                ws,
                "I34",
                (
                    "SI__X__   NO____"
                    if monthly_report.has_electronic_invoice
                    else "SI____   NO__X__"
                ),
            )
            self.write_to_cell(
                ws,
                "J34",
                (
                    monthly_report.invoice_number
                    if monthly_report.has_electronic_invoice
                    else "N/A"
                ),
            )
            if initial_report:
                self.write_to_cell(
                    ws,
                    "F34",
                    social_security.arl_contribution if social_security else "N/A",
                )
            self.write_to_cell(
                ws, "B38", monthly_report.contractor_account_number or "N/A"
            )
            self.write_to_cell(
                ws,
                "F38",
                (
                    monthly_report.contractor_bank_name
                    if monthly_report.contractor_bank_name
                    else "N/A"
                ),
            )
            self.write_to_cell(
                ws,
                "I38",
                (
                    monthly_report.contractor_account_type
                    if monthly_report.contractor_account_type
                    else "N/A"
                ),
            )

            try:
                initial_doc = self._initial_report_documentation_service.get_by_contractor_contract_id(
                    monthly_report.contractor_contract_id
                )
                if initial_doc:
                    self._insert_contractor_signature(
                        ws, initial_doc.signature_file_key, monthly_report
                    )
            except Exception as e:
                print(f"Error adding contractor signature: {str(e)}")
                self.write_to_cell(
                    ws, "B88", f"NOMBRE: {monthly_report.contractor_full_name}"
                )
                self.write_to_cell(
                    ws, "B89", f"C.C.: {monthly_report.contractor_id_number}"
                )

            monthly_report_supervisors = (
                self._monthly_report_supervisor_service.get_by_monthly_report_id(
                    monthly_report.id
                )
            )

            if len(monthly_report_supervisors) > 0:
                supervisor = monthly_report_supervisors[0]

                signature_key = None
                contract = monthly_report.contractor_contract.contract
                if contract:
                    supervisor_contracts = (
                        self._supervisor_contract_service.get_by_contract_id(
                            contract.id
                        )
                    )
                    for sc in supervisor_contracts:
                        if (
                            sc.supervisor
                            and str(sc.supervisor.id_number)
                            == supervisor.supervisor_id_number
                        ):
                            signature_key = sc.supervisor.signature_file_key
                            break

                self._insert_supervisor_signature(
                    ws,
                    signature_key,
                    "H85",
                    supervisor.supervisor_full_name,
                    monthly_report,
                    (
                        supervisor.approval_date.strftime("%d/%m/%Y %H:%M")
                        if supervisor.approval_date
                        else None
                    ),
                )

                self.write_to_cell(
                    ws,
                    "I88",
                    f"NOMBRE SUPERVISOR: {supervisor.supervisor_full_name}",
                )
                self.write_to_cell(
                    ws, "I89", f"C.C.: {supervisor.supervisor_id_number}"
                )
                self.write_to_cell(
                    ws, "I90", f"CARGO: {supervisor.supervisor_position or 'N/A'}"
                )

            if len(monthly_report_supervisors) > 1:
                supervisor = monthly_report_supervisors[1]

                signature_key = None
                contract = monthly_report.contractor_contract.contract
                if contract:
                    supervisor_contracts = (
                        self._supervisor_contract_service.get_by_contract_id(
                            contract.id
                        )
                    )
                    for sc in supervisor_contracts:
                        if (
                            sc.supervisor
                            and str(sc.supervisor.id_number)
                            == supervisor.supervisor_id_number
                        ):
                            signature_key = sc.supervisor.signature_file_key
                            break

                self._insert_supervisor_signature(
                    ws,
                    signature_key,
                    "H91",
                    supervisor.supervisor_full_name,
                    monthly_report,
                    (
                        supervisor.approval_date.strftime("%d/%m/%Y %H:%M")
                        if supervisor.approval_date
                        else None
                    ),
                )

                self.write_to_cell(
                    ws, "I94", f"NOMBRE SUPERVISOR: {supervisor.supervisor_full_name}"
                )
                self.write_to_cell(
                    ws, "I95", f"C.C.: {supervisor.supervisor_id_number}"
                )
                self.write_to_cell(
                    ws, "I96", f"CARGO: {supervisor.supervisor_position or 'N/A'}"
                )
            else:
                self.write_to_cell(ws, "H91", "No aplica")
                self.write_to_cell(ws, "I94", "NOMBRE SUPERVISOR: No aplica")
                self.write_to_cell(ws, "I95", "C.C.: No aplica")
                self.write_to_cell(ws, "I96", "CARGO: No aplica")

            self.write_to_cell(ws, "F75", "No aplica")
            self.write_to_cell(ws, "F76", "No aplica")
            self.write_to_cell(ws, "C13", str(monthly_report.report_number))
            self.write_to_cell(
                ws,
                "E13",
                str(monthly_report.total_payments),
            )
            all_payments = []
            previous_reports = (
                self._monthly_report_repository.get_by_contractor_contract_id(
                    monthly_report.contractor_contract_id
                )
            )
            for report in previous_reports:
                if report.report_number < monthly_report.report_number:
                    payments = self._payment_service.get_by_monthly_report_id(report.id)
                    all_payments.extend(payments)
            sorted_payments = sorted(all_payments, key=lambda x: x.payment_number)
            for payment in sorted_payments:
                if payment.payment_number <= 10:
                    cell = f"{get_column_letter(payment.payment_number)}25"
                    self.write_to_cell(ws, cell, payment.value)
                elif payment.payment_number <= 20:
                    cell = f"{get_column_letter(payment.payment_number - 10)}26"
                    self.write_to_cell(ws, cell, payment.value)

            total_paid = sum(payment.value for payment in sorted_payments)

            reductions_value = 0
            if contract and hasattr(contract, "reduction") and contract.reduction:
                reductions_value = sum(r.value_redution for r in contract.reduction)

            total_value = initial_value + additions_value - reductions_value
            saldo_pendiente = total_value - total_paid

            self.write_to_cell(ws, "D16", initial_value)
            self.write_to_cell(ws, "D17", additions_value or "")
            self.write_to_cell(ws, "D18", reductions_value or "")
            self.write_to_cell(ws, "D19", total_value)
            self.write_to_cell(ws, "D20", total_paid)
            self.write_to_cell(ws, "D21", saldo_pendiente)

            if contract:
                period = self._period_service.get_periods_by_contract(
                    contract.id, monthly_report.report_number
                )
                if period:
                    self.write_to_cell(ws, "C36", period.period_payment)
                else:
                    self.write_to_cell(ws, "C36", 0)

            current_row = 52
            for item in detailed_obligations:
                report_obligation = item["report_obligation"]
                obligation = item["obligation_detail"]
                obligation_text = (
                    f"{obligation.number}. {obligation.name}"
                    if obligation.number is not None
                    else obligation.name
                )
                self.write_to_cell(ws, f"A{current_row}", obligation_text)
                self.write_to_cell(ws, f"E{current_row}", report_obligation.description)
                self.write_to_cell(
                    ws,
                    f"I{current_row}",
                    "Los soportes de las actividades son cargadas en ARCA y en secop II",
                )
                description = str(report_obligation.description or "")
                manual_line_breaks = description.count("\n")

                lines_based_on_length = 0
                if manual_line_breaks > 0:
                    parts = description.split("\n")
                    for part in parts:
                        lines_based_on_length += (len(part) // 50) + 1
                else:
                    lines_based_on_length = (len(description) // 50) + 1

                estimated_lines = lines_based_on_length

                row_height = max(30, estimated_lines * 15)
                ws.row_dimensions[current_row].height = row_height
                current_row += 1
            for row in range(current_row, 72):
                ws.row_dimensions[row].hidden = True
            monthly_report_ccps = (
                self._monthly_report_ccp_service.get_by_monthly_report_id(
                    monthly_report.id
                )
            )
            current_row = 43
            for report_ccp in monthly_report_ccps:
                self.write_to_cell(
                    ws, f"A{current_row}", report_ccp.ccp.expense_object_use_ccp
                )
                self.write_to_cell(
                    ws, f"D{current_row}", report_ccp.ccp.expense_object_description
                )
                self.write_to_cell(ws, f"K{current_row}", report_ccp.ccp_value)
                current_row += 1
            if monthly_report_ccps:
                total_value = sum(ccp.ccp_value for ccp in monthly_report_ccps)
                self.write_to_cell(ws, "K48", total_value)
            if monthly_report.observations:
                self.write_to_cell(ws, "A79", monthly_report.observations)
            wb.save(filled_excel_path)
            wb.close()

            pdf_path = filled_excel_path.with_suffix(".pdf")
            output_dir = pdf_path.parent

            try:
                is_windows = os.name == "nt"
                try_convert = True

                if is_windows:
                    excel = None
                    wb = None
                    pythoncom_initialized = False
                    try:
                        import pythoncom
                        import win32com.client

                        pythoncom.CoInitialize()
                        pythoncom_initialized = True
                        excel = win32com.client.Dispatch("Excel.Application")
                        excel.Visible = False
                        wb = excel.Workbooks.Open(str(filled_excel_path.absolute()))
                        wb.ExportAsFixedFormat(0, str(pdf_path.absolute()))
                    except Exception as e:
                        print(f"DEBUG: win32com PDF conversion failed: {e}")
                        try_convert = False
                    finally:
                        if wb:
                            wb.Close(SaveChanges=False)
                        if excel:
                            excel.Quit()
                        if pythoncom_initialized:
                            pythoncom.CoUninitialize()
                else:
                    try:
                        subprocess.run(
                            [
                                "/usr/bin/soffice",
                                "--headless",
                                "--convert-to",
                                "pdf",
                                "--outdir",
                                str(output_dir),
                                str(filled_excel_path),
                            ],
                            capture_output=True,
                            text=True,
                            check=True,
                        )
                    except Exception as e:
                        print(f"DEBUG: soffice PDF conversion failed: {e}")
                        try_convert = False

                if try_convert and os.path.exists(pdf_path):
                    with open(pdf_path, "rb") as pdf_file:
                        pdf_content = pdf_file.read()

                    os.remove(pdf_path)
                    os.remove(filled_excel_path)
                    return pdf_content
                else:
                    with open(filled_excel_path, "rb") as excel_file:
                        excel_content = excel_file.read()

                    os.remove(filled_excel_path)
                    return excel_content
            except Exception as e:
                if os.path.exists(filled_excel_path):
                    os.remove(filled_excel_path)
                if os.path.exists(pdf_path):
                    os.remove(pdf_path)
                raise ValidationError(f"Error processing report: {str(e)}")
        except ValidationError:
            raise
        except Exception as e:
            if os.path.exists(filled_excel_path):
                os.remove(filled_excel_path)
            raise ValidationError(f"Error generating report: {str(e)}")
