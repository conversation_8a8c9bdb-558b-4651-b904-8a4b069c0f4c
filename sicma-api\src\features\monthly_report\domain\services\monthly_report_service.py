import io
from datetime import datetime
from typing import List, Optional

from PyPDF2 import PdfMerger
from sqlalchemy.exc import IntegrityError
from src.features.contractor_contract.domain.services import ContractorContractService
from src.features.initial_report_documentation.domain.services import (
    InitialReportDocumentationService,
)
from src.features.monthly_report.domain.entities.monthly_report_supervisor_export_entity import (
    MonthlyReportSupervisorExportEntity,
)
from src.features.monthly_report_supervisor.domain.services.monthly_report_supervisor_service import (
    MonthlyReportSupervisorService,
)
from src.features.payment.domain.entities import CreatePaymentEntity
from src.features.payment.domain.services import PaymentService
from src.features.period.domain.services.period_service import PeriodService
from src.features.report_review_history.domain.entities import (
    CreateReportReviewHistoryEntity,
)
from src.features.report_review_history.domain.services import (
    ReportReviewHistoryService,
)
from src.features.report_review_status.domain.services import ReportReviewStatusService
from src.features.social_security_contribution.domain.services import (
    SocialSecurityContributionService,
)
from src.features.supervisor_contract.domain.services.supervisor_contract_service import (
    SupervisorContractService,
)
from src.utils.exceptions import (
    DuplicateError,
    ForeignKeyNotFoundError,
    ForeignKeyViolationError,
    NotFoundError,
)
from src.utils.services.local_file_service import LocalFileService

from ..contracts import IMonthlyReportRepository
from ..entities import (
    CreateMonthlyReportEntity,
    MonthlyReportEntity,
    UpdateMonthlyReportEntity,
)
from .monthly_report_pdf_generator_service import MonthlyReportPDFGeneratorService


class MonthlyReportService:
    def __init__(
        self,
        monthly_report_repository: IMonthlyReportRepository,
        contractor_contract_service: ContractorContractService,
        period_service: PeriodService,
        report_review_status_service: ReportReviewStatusService,
        report_review_history_service: ReportReviewHistoryService,
        monthly_report_pdf_generator_service: MonthlyReportPDFGeneratorService,
        payment_service: PaymentService,
        social_security_contribution_service: SocialSecurityContributionService,
        file_storage_service: LocalFileService,
        initial_report_documentation_service: InitialReportDocumentationService,
        supervisor_contract_service: SupervisorContractService,
        monthly_report_supervisor_service: Optional[
            MonthlyReportSupervisorService
        ] = None,
    ):
        self._monthly_report_repository = monthly_report_repository
        self._contractor_contract_service = contractor_contract_service
        self._period_service = period_service
        self._report_review_status_service = report_review_status_service
        self._report_review_history_service = report_review_history_service
        self._monthly_report_pdf_generator_service = (
            monthly_report_pdf_generator_service
        )
        self._payment_service = payment_service
        self._social_security_contribution_service = (
            social_security_contribution_service
        )
        self._file_storage_service = file_storage_service
        self._initial_report_documentation_service = (
            initial_report_documentation_service
        )
        self._supervisor_contract_service = supervisor_contract_service
        self._monthly_report_supervisor_service = monthly_report_supervisor_service

    def get_all(self) -> List[MonthlyReportEntity]:
        prueba = self._monthly_report_repository.get_all()
        return prueba

    def get_by_id(self, id: int) -> MonthlyReportEntity:
        entity = self._monthly_report_repository.get_by_id(id)
        if entity is None:
            raise NotFoundError(f"Monthly Report with id {id} not found")
        return entity

    def get_by_contractor_contract_id(
        self, contractor_contract_id: int
    ) -> List[MonthlyReportEntity]:
        # Obtiene todos los reportes asociados al contrato del contratista
        reports = self._monthly_report_repository.get_by_contractor_contract_id(
            contractor_contract_id
        )
        #  print("reports", reports)
        # Filtra los reportes cuyo estado actual es 4
        # print("SinFiltrado", len(reportsResult))
        # reports = [
        #  reportlist
        #     for reportlist in reportsResult
        #    if reportlist.current_review_status and reportlist.current_review_status.id != 4
        # ]
        # print("Filtrado", len(reports))

        # Obtiene todos los contratos de supervisor asociados a este contrato
        contractor_contract = self._contractor_contract_service.get_by_id(
            contractor_contract_id
        )
        supervisor_contracts = []
        if contractor_contract and contractor_contract.contract_id:
            result = self._supervisor_contract_service.get_by_contract_id(
                contractor_contract.contract_id
            )
            supervisor_contracts = result if result is not None else []

        cantidad = len(supervisor_contracts)

        # Si hay más de dos supervisores asociados, ajusta el estado de revisión de cada reporte
        if cantidad >= 2:
            print("Llegue a mas de dos supervisores")
            for report in reports:

                # Obtiene el historial de revisiones del reporte, ordenado por fecha descendente
                review_history = (
                    self._report_review_history_service.get_by_monthly_report_id(
                        report.id
                    )
                )

                review_history_sorted = sorted(
                    review_history, key=lambda x: x.review_date, reverse=True
                )

                if report.double_supervision:
                    last_two_reviews = review_history_sorted[:2]

                    # Si las dos últimas revisiones son "Aprobado" (estado 3), deja el estado actual como aprobado
                    if all(h.review_status_id == 3 for h in last_two_reviews):
                        print("entre Aprobado")

                        if report.current_review_status is not None:
                            report.current_review_status.id = 3
                            report.current_review_status.name = "Aprobado"
                    else:
                        print("entre otro estado")
                        # Si alguna de las dos últimas no es "Aprobado", busca el último estado diferente de 3
                        last_not_approved = next(
                            (
                                h
                                for h in review_history_sorted
                                if h.review_status_id != 3
                            ),
                            None,
                        )
                        if last_not_approved:
                            report.current_review_status.id = (
                                last_not_approved.review_status_id
                            )
                            # Obtiene el nombre del estado correspondiente y lo asigna
                            status = self._report_review_status_service.get_by_id(
                                last_not_approved.review_status_id
                            )
                            print("Estado:", status)
                            report.current_review_status.name = status.name
                    print("Entre a double supervision")
                else:
                    print("No entre a double supervision")

            # Retorna la lista de reportes (con los estados ajustados si aplica)
        return reports

    def create(self, create_entity: CreateMonthlyReportEntity) -> MonthlyReportEntity:
        try:

            contractor_contract = self._contractor_contract_service.get_by_id(
                create_entity.contractor_contract_id
            )
            total_payments = self._period_service.get_periods_number(
                contractor_contract.contract.id
            )

            create_entity_dict = create_entity.model_dump()
            create_entity_dict["total_payments"] = total_payments

            existing_reports = self.get_by_contractor_contract_id(
                create_entity.contractor_contract_id
            )

            if existing_reports:
                latest_report = max(existing_reports, key=lambda x: x.report_number)
                for field in [
                    "contract_number",
                    "contract_year",
                    "contract_duration_days",
                    "contract_start_date",
                    "contract_end_date",
                    "contract_total_value",
                    "contractor_full_name",
                    "contractor_id_number",
                    "contractor_email",
                    "contractor_phone",
                    "contract_dependency",
                    "contract_group",
                    "contractor_bank_name",
                    "contractor_account_number",
                    "contractor_account_type",
                ]:
                    if hasattr(latest_report, field):
                        create_entity_dict[field] = getattr(latest_report, field)

            contract = contractor_contract.contract
            if contract:
                supervisor_contracts = (
                    self._supervisor_contract_service.get_by_contract_id(contract.id)
                )
                if supervisor_contracts:
                    supervisor = supervisor_contracts[0].supervisor
                    create_entity_dict["supervisor_full_name"] = supervisor.full_name
                    create_entity_dict["supervisor_id_number"] = str(
                        supervisor.id_number
                    )
                    create_entity_dict["supervisor_position"] = supervisor.position

            create_entity = CreateMonthlyReportEntity(**create_entity_dict)
            monthly_report = self._monthly_report_repository.create(create_entity)

            contractor_bank = (
                contractor_contract.contractor.contractor_banks[0]
                if contractor_contract.contractor.contractor_banks
                else None
            )

            contract_values = contractor_contract.contract.contract_values
            first_contract_value = contract_values[0] if contract_values else None
            last_contract_value = contract_values[-1] if contract_values else None

            periods = self._period_service.get_periods(contractor_contract.contract.id)

            current_period = next(
                (p for p in periods if p.period_num == monthly_report.report_number),
                None,
            )

            payment_entity = CreatePaymentEntity(
                payment_number=monthly_report.report_number,
                payment_date=monthly_report.end_date,
                value=current_period.period_payment if current_period else 0,
                initial_value=(
                    first_contract_value.numeric_value if first_contract_value else 0
                ),
                total_value=(
                    last_contract_value.numeric_value if last_contract_value else 0
                ),
                paid_value=0,
                additions=(
                    sum(cv.numeric_value for cv in contract_values[1:])
                    if len(contract_values) > 1
                    else 0
                ),
                period_start=(
                    current_period.period_start_date
                    if current_period
                    else monthly_report.start_date
                ),
                period_end=(
                    current_period.period_end_date
                    if current_period
                    else monthly_report.end_date
                ),
                total_days=(
                    current_period.period_worked_days_in_month
                    if current_period
                    else (monthly_report.end_date - monthly_report.start_date).days + 1
                ),
                account_number=(
                    contractor_bank.account_number if contractor_bank else None
                ),
                monthly_report_id=monthly_report.id,
                bank_id=contractor_bank.bank_id if contractor_bank else None,
                bank_account_type_id=(
                    contractor_bank.bank_account_type_id if contractor_bank else 1
                ),
            )

            self._payment_service.create(payment_entity)

            return self.get_by_id(monthly_report.id)

        except IntegrityError as e:
            if "duplicate key value violates unique constraint" in str(e):
                raise DuplicateError(
                    f"Monthly Report with these details already exists"
                )
            elif "violates foreign key constraint" in str(e):
                raise ForeignKeyNotFoundError(f"Related entity not found.")
            else:
                raise ForeignKeyViolationError(
                    f"Error creating Monthly Report: {str(e)}"
                )

    def create_max(
        self, create_entity_list: CreateMonthlyReportEntity
    ) -> MonthlyReportEntity:
        try:

            create_entity: CreateMonthlyReportEntity

            contractor_contract = self._contractor_contract_service.get_by_id(
                create_entity_list.contractor_contract_id
            )

            print("contractor_contract", contractor_contract)

            print("Contractor Contract:", create_entity_list.report_number)

            for i in range(1, create_entity_list.report_number + 1):
                print("Count:", i)
                prueba = self._period_service.get_periods_by_contract(
                    contractor_contract.contract_id, i
                )
                print("Prueba:", prueba)

                # Construye el diccionario para el nuevo reporte
                create_entity_dict = create_entity_list.model_dump()
                create_entity_dict["contractor_contract_id"] = (
                    create_entity_list.contractor_contract_id
                )
                create_entity_dict["report_number"] = i
                create_entity_dict["total_value"] = prueba.period_payment
                create_entity_dict["start_date"] = prueba.period_start_date
                create_entity_dict["end_date"] = prueba.period_end_date

                total_payments = self._period_service.get_periods_number(
                    contractor_contract.contract.id
                )
                print("Total Payments:", total_payments)
                create_entity_dict["total_payments"] = total_payments

                existing_reports = self.get_by_contractor_contract_id(
                    create_entity_list.contractor_contract_id
                )

                if existing_reports:
                    latest_report = max(existing_reports, key=lambda x: x.report_number)
                    for field in [
                        "contract_number",
                        "contract_year",
                        "contract_duration_days",
                        "contract_start_date",
                        "contract_end_date",
                        "contract_total_value",
                        "contractor_full_name",
                        "contractor_id_number",
                        "contractor_email",
                        "contractor_phone",
                        "contract_dependency",
                        "contract_group",
                        "contractor_bank_name",
                        "contractor_account_number",
                        "contractor_account_type",
                    ]:
                        if hasattr(latest_report, field):
                            create_entity_dict[field] = getattr(latest_report, field)
                else:
                    contractor = contractor_contract.contractor
                    create_entity_dict["contractor_full_name"] = getattr(
                        contractor, "full_name", None
                    )
                    create_entity_dict["contractor_id_number"] = getattr(
                        contractor, "id_number", None
                    )
                    create_entity_dict["contractor_email"] = getattr(
                        contractor, "email", None
                    )
                    create_entity_dict["contractor_phone"] = getattr(
                        contractor, "phone", None
                    )
                    if (
                        hasattr(contractor, "contractor_banks")
                        and contractor.contractor_banks
                        and len(contractor.contractor_banks) > 0
                    ):
                        bank = contractor.contractor_banks[0]
                        create_entity_dict["contractor_bank_name"] = getattr(
                            bank, "bank_name", None
                        )
                        create_entity_dict["contractor_account_number"] = getattr(
                            bank, "account_number", None
                        )
                        create_entity_dict["contractor_account_type"] = getattr(
                            bank, "account_type", None
                        )

                contract = contractor_contract.contract
                if contract:
                    supervisor_contracts = (
                        self._supervisor_contract_service.get_by_contract_id(
                            contract.id
                        )
                    )
                    if supervisor_contracts:
                        supervisor = supervisor_contracts[0].supervisor
                        create_entity_dict["supervisor_full_name"] = (
                            supervisor.full_name
                        )
                        create_entity_dict["supervisor_id_number"] = str(
                            supervisor.id_number
                        )
                        create_entity_dict["supervisor_position"] = supervisor.position

                create_entity = CreateMonthlyReportEntity(**create_entity_dict)
                monthly_report = self._monthly_report_repository.create(create_entity)

                contractor_bank = (
                    contractor_contract.contractor.contractor_banks[0]
                    if contractor_contract.contractor.contractor_banks
                    else None
                )

                contract_values = contractor_contract.contract.contract_values
                first_contract_value = contract_values[0] if contract_values else None
                last_contract_value = contract_values[-1] if contract_values else None

                periods = self._period_service.get_periods(
                    contractor_contract.contract.id
                )

                current_period = next(
                    (
                        p
                        for p in periods
                        if p.period_num == monthly_report.report_number
                    ),
                    None,
                )

                payment_entity = CreatePaymentEntity(
                    payment_number=monthly_report.report_number,
                    payment_date=monthly_report.end_date,
                    value=current_period.period_payment if current_period else 0,
                    initial_value=(
                        first_contract_value.numeric_value
                        if first_contract_value
                        else 0
                    ),
                    total_value=(
                        last_contract_value.numeric_value if last_contract_value else 0
                    ),
                    paid_value=0,
                    additions=(
                        sum(cv.numeric_value for cv in contract_values[1:])
                        if len(contract_values) > 1
                        else 0
                    ),
                    period_start=(
                        current_period.period_start_date
                        if current_period
                        else monthly_report.start_date
                    ),
                    period_end=(
                        current_period.period_end_date
                        if current_period
                        else monthly_report.end_date
                    ),
                    total_days=(
                        current_period.period_worked_days_in_month
                        if current_period
                        else (monthly_report.end_date - monthly_report.start_date).days
                        + 1
                    ),
                    account_number=(
                        contractor_bank.account_number if contractor_bank else None
                    ),
                    monthly_report_id=monthly_report.id,
                    bank_id=contractor_bank.bank_id if contractor_bank else None,
                    bank_account_type_id=(
                        contractor_bank.bank_account_type_id if contractor_bank else 1
                    ),
                )

                self._payment_service.create(payment_entity)
                print("Llegue aqui", monthly_report.id)
                if i == create_entity_list.report_number:
                    self._report_review_history_service.create(
                        CreateReportReviewHistoryEntity(
                            monthly_report_id=monthly_report.id,
                            review_status_id=1,  # Cambia por el ID de estado que corresponda
                            review_date=datetime.now(),
                            comment="Creación automática del historial de revisión",
                        )
                    )

                else:
                    self._report_review_history_service.create(
                        CreateReportReviewHistoryEntity(
                            monthly_report_id=monthly_report.id,
                            review_status_id=5,  # Cambia por el ID de estado que corresponda
                            review_date=datetime.now(),
                            comment="Creación automática del historial de revisión",
                        )
                    )

            return self.get_by_id(monthly_report.id)

        except IntegrityError as e:
            if "duplicate key value violates unique constraint" in str(e):
                raise DuplicateError(
                    f"Monthly Report with these details already exists"
                )
            elif "violates foreign key constraint" in str(e):
                raise ForeignKeyNotFoundError(f"Related entity not found.")
            else:
                raise ForeignKeyViolationError(
                    f"Error creating Monthly Report: {str(e)}"
                )

    def update(
        self, id: int, update_entity: UpdateMonthlyReportEntity
    ) -> MonthlyReportEntity:
        self.get_by_id(id)
        try:
            return self._monthly_report_repository.update(id, update_entity)
        except IntegrityError as e:
            if "duplicate key value violates unique constraint" in str(e):
                raise DuplicateError(
                    f"Monthly Report with these details already exists"
                )
            elif "violates foreign key constraint" in str(e):
                raise ForeignKeyNotFoundError(f"Related entity not found.")
            else:
                raise ForeignKeyViolationError(
                    f"Error updating Monthly Report: {str(e)}"
                )

    def delete(self, id: int) -> None:
        self.get_by_id(id)
        try:
            self._monthly_report_repository.delete(id)
        except IntegrityError:
            raise ForeignKeyViolationError(
                f"Monthly Report with id {id} is still referenced by other tables and cannot be deleted."
            )

    def change_report_status(
        self, report_id: int, new_status_name: str
    ) -> MonthlyReportEntity:
        monthly_report = self.get_by_id(report_id)
        new_status = self._report_review_status_service.get_by_name(new_status_name)
        review_date = datetime.now()

        review_history = CreateReportReviewHistoryEntity(
            monthly_report_id=report_id,
            review_status_id=new_status.id,
            review_date=review_date,
            comment=f"Estado cambiado a {new_status_name}",
        )
        self._report_review_history_service.create(review_history)

        # Handle monthly report supervisor records based on status
        if self._monthly_report_supervisor_service is not None:
            # If status is "Rechazado" (rejected), remove any supervisor entries for this report
            if new_status_name.lower() == "rechazado":
                # Get current supervisors for this report
                current_supervisors = (
                    self._monthly_report_supervisor_service.get_by_monthly_report_id(
                        report_id
                    )
                )
                # Delete each supervisor record
                for supervisor in current_supervisors:
                    self._monthly_report_supervisor_service.delete(supervisor.id)

            # If the new status is "Aprobado", add or update supervisor information with approval date
            elif (
                new_status_name.lower() == "aprobado"
                and monthly_report.contractor_contract
                and monthly_report.contractor_contract.contract
            ):
                # Get current supervisors for this report
                current_supervisors = (
                    self._monthly_report_supervisor_service.get_by_monthly_report_id(
                        report_id
                    )
                )

                # Get contract supervisors
                contract = monthly_report.contractor_contract.contract
                supervisor_contracts = (
                    self._supervisor_contract_service.get_by_contract_id(contract.id)
                )

                # If we have supervisors for this contract
                if supervisor_contracts:
                    for i, sc in enumerate(
                        supervisor_contracts[:2]
                    ):  # Only support up to 2 supervisors
                        if sc.supervisor:
                            # Check if this supervisor already exists for this report
                            matching_supervisor = next(
                                (
                                    s
                                    for s in current_supervisors
                                    if s.supervisor_id_number
                                    == str(sc.supervisor.id_number)
                                ),
                                None,
                            )

                            from src.features.monthly_report_supervisor.domain.entities.monthly_report_supervisor_entity import (
                                CreateMonthlyReportSupervisorEntity,
                                UpdateMonthlyReportSupervisorEntity,
                            )

                            if matching_supervisor:
                                update_entity = UpdateMonthlyReportSupervisorEntity(
                                    approval_date=review_date
                                )
                                self._monthly_report_supervisor_service.update(
                                    matching_supervisor.id, update_entity
                                )
                            else:
                                create_entity = CreateMonthlyReportSupervisorEntity(
                                    monthly_report_id=report_id,
                                    supervisor_id_number=str(sc.supervisor.id_number),
                                    supervisor_full_name=sc.supervisor.full_name,
                                    supervisor_position=sc.supervisor.position,
                                    approval_date=review_date,
                                )
                                self._monthly_report_supervisor_service.create(
                                    create_entity
                                )

        return self.get_by_id(report_id)

    def generate_pdf(self, report_id: int) -> bytes:
        monthly_report = self.get_by_id(report_id)

        monthly_report_pdf = (
            self._monthly_report_pdf_generator_service.generate_monthly_report_pdf(
                monthly_report=monthly_report
            )
        )

        merger = PdfMerger()

        monthly_report_stream = io.BytesIO(monthly_report_pdf)
        merger.append(monthly_report_stream)

        try:
            if monthly_report.report_number == 1:
                initial_doc = self._initial_report_documentation_service.get_by_contractor_contract_id(
                    monthly_report.contractor_contract_id
                )

                pdf_files = [
                    ("eps_certificate_file_key", "EPS certificate"),
                    ("arl_certificate_file_key", "ARL certificate"),
                    ("pension_certificate_file_key", "Pension certificate"),
                    ("bank_certificate_file_key", "Bank certificate"),
                    ("tax_form_file_key", "Tax form"),
                    ("dependents_support_file_key", "Dependents support"),
                    ("housing_interest_file_key", "Housing interest"),
                    ("prepaid_medicine_file_key", "Prepaid medicine"),
                    ("afc_account_file_key", "AFC account"),
                    ("voluntary_savings_file_key", "Voluntary savings"),
                ]

                for file_key_attr, _ in pdf_files:
                    file_key = getattr(initial_doc, file_key_attr, None)
                    if file_key:
                        try:
                            pdf_content = self._file_storage_service.get_pdf_file(
                                file_key
                            )
                            pdf_stream = io.BytesIO(pdf_content)
                            merger.append(pdf_stream)
                        except Exception:
                            continue

            social_security = (
                self._social_security_contribution_service.get_by_monthly_report_id(
                    report_id
                )
            )
            if social_security and social_security.certificate_file_key:
                social_security_pdf = self._file_storage_service.get_pdf_file(
                    social_security.certificate_file_key
                )
                social_security_stream = io.BytesIO(social_security_pdf)
                merger.append(social_security_stream)

        except NotFoundError:
            pass
        except Exception:
            pass

        output = io.BytesIO()
        merger.write(output)
        merger.close()

        return output.getvalue()

    def get_monthly_report_by_supervisor(
        self, supervisor_email: str
    ) -> List[MonthlyReportSupervisorExportEntity]:
        return self._monthly_report_repository.get_monthly_report_by_supervisor(
            supervisor_email
        )

    def get_previous_period_payment(
        self, contract_id: int, current_num_payment: int
    ) -> Optional[int]:
        if current_num_payment <= 1:
            return None

        previo = (
            self._monthly_report_repository.get_by_contractor_contract_num_payment_id(
                contract_id, current_num_payment - 1
            )
        )
        return previo.total_value
