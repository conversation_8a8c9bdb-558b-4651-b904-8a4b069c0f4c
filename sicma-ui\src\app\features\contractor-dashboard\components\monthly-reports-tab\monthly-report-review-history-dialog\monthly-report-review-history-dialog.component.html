<div class="dialog-content">
  <button
    mat-icon-button
    class="close-button"
    (click)="close()"
    matTooltip="Cerrar"
  >
    <mat-icon>close</mat-icon>
  </button>

  <h2 class="section-title">Historial del Informe</h2>

  @if (reviewHistory.length === 0) {
    <div class="no-data-message">
      No hay historial de revisiones disponible para este informe.
    </div>
  } @else {
    <mat-card appearance="outlined" class="table-card">
      <mat-card-content>
        <table mat-table [dataSource]="reviewHistory" class="history-table">
          <ng-container matColumnDef="reviewDate">
            <th mat-header-cell *matHeaderCellDef>Fecha y <PERSON>ra</th>
            <td mat-cell *matCellDef="let item">
              {{ item.reviewDate | date: "dd/MM/yyyy HH:mm" }}
            </td>
          </ng-container>

          <ng-container matColumnDef="reviewer">
            <th mat-header-cell *matHeaderCellDef>Usuario</th>
            <td mat-cell *matCellDef="let item">
              {{ item.reviewer?.username || "Sin usuario" }}
            </td>
          </ng-container>

          <ng-container matColumnDef="reviewStatus">
            <th mat-header-cell *matHeaderCellDef>Estado</th>
            <td mat-cell *matCellDef="let item">
              {{ item.reviewStatus?.name }}
            </td>
          </ng-container>

          <ng-container matColumnDef="comment">
            <th mat-header-cell *matHeaderCellDef>Comentarios</th>
            <td mat-cell *matCellDef="let item" class="comment-cell">
              {{ item.comment || "Sin comentarios" }}
            </td>
          </ng-container>

          <tr
            mat-header-row
            *matHeaderRowDef="displayedColumns; sticky: true"
          ></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </mat-card-content>
    </mat-card>
  }
</div>
